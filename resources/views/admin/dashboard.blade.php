@extends('layouts.admin')

@section('title', 'Admin Dashboard - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Admin Dashboard</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Admin Dashboard</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- Welcome Section -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-lg bg-primary-transparent">
                                <i class="ti ti-shield-check fs-18"></i>
                            </span>
                        </div>
                        <div>
                            <h5 class="fw-semibold mb-1">Welcome back, {{ auth()->user()->name ?? 'Admin' }}!</h5>
                            <p class="text-muted mb-0">You have full administrative access to SMP Online.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row gy-4">
        <!-- Total Users Card -->
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-info-transparent">
                                <i class="ti ti-users fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <h6 class="fw-semibold mb-1">Total Users</h6>
                            <h4 class="fw-bold text-info mb-0">{{ \App\Models\User::count() }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Admin Users Card -->
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-info-transparent">
                                <i class="ti ti-shield-check fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <h6 class="fw-semibold mb-1">Administrators</h6>
                            <h4 class="fw-bold text-info mb-0">{{ \App\Models\User::where('role', 'admin')->count() }}
                            </h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Member Card -->
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-info-transparent">
                                <i class="ti ti-user fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <h6 class="fw-semibold mb-1">Members</h6>
                            <h4 class="fw-bold text-info mb-0">
                                {{ \App\Models\User::where('role', 'member')->count() }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Client Users Card -->
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-info-transparent">
                                <i class="ti ti-briefcase fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <h6 class="fw-semibold mb-1">Users</h6>
                            <h4 class="fw-bold text-info mb-0">{{ \App\Models\User::where('role', 'user')->count() }}
                            </h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Available Resources Statistics -->
    <div class="row gy-4">
        <!-- Available Fields Card -->
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-success-transparent">
                                <i class="bx bx-football-pitch fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <h6 class="fw-semibold mb-1">Active Fields</h6>
                            <h4 class="fw-bold text-success mb-0">{{ \App\Models\Field::active()->count() }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inactive Fields Card -->
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-danger-transparent">
                                <i class="bx bx-football-pitch fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <h6 class="fw-semibold mb-1">Inactive Fields</h6>
                            <h4 class="fw-bold text-danger mb-0">{{ \App\Models\Field::where('status', 'Inactive')->count() }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Available Utilities Card -->
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-success-transparent">
                                <i class="bx bx-bench fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <h6 class="fw-semibold mb-1">Active Utilities</h6>
                            <h4 class="fw-bold text-success mb-0">{{ \App\Models\Utility::active()->count() }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inactive Utilities Card -->
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-danger-transparent">
                                <i class="bx bx-bench fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <h6 class="fw-semibold mb-1">Inactive Utilities</h6>
                            <h4 class="fw-bold text-danger mb-0">{{ \App\Models\Utility::where('is_active', false)->count() }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-----Weekly Reservations Table------>
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="ti ti-list me-2"></i>Upcoming reservations this week
                    </div>
                </div>
                @php
                    use App\Models\Reservation;
                    use Carbon\Carbon;
                    $query = Reservation::with('field', 'user');
                    $reservations = $query
                        ->whereIn('status', ['Pending', 'Confirmed', 'Cancelled']) // adjust statuses as needed
                        ->whereBetween('booking_date', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])
                        ->orderBy('booking_date', 'asc')
                        ->orderBy('start_time', 'asc')
                        ->paginate(10);
                @endphp

                <div class="card-body">
                    @if ($reservations->count() > 0)

                        <div class="table-responsive">
                            <table class="table text-nowrap table-hover">
                                <thead>
                                    <tr>
                                        <th>Field</th>
                                        <th>Date & Time</th>
                                        <th>Customer</th>
                                        <th>Cost</th>
                                        <th>Status</th>
                                        <!--<th>Actions</th>-->
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($reservations as $reservation)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-2">
                                                        <span class="avatar avatar-sm bg-light">
                                                            <i class="ti ti-building-stadium fs-12"></i>
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <span class="fw-semibold">{{ $reservation->field->name }}</span>
                                                        <br>
                                                        <small class="text-muted">{{ $reservation->field->type }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <span class="fw-semibold">{{ $reservation->booking_date->format('M d, Y') }}</span>
                                                    <br>
                                                    <small class="text-muted">{{ $reservation->time_range }}</small>
                                                    <br>
                                                    <span class="badge bg-primary-transparent text-primary">Duration: {{ $reservation->duration_hours }} {{ Str::plural('hour', $reservation->duration_hours) }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-semibold">{{ $reservation->customer_display_name }}</span>
                                            </td>
                                            <td>
                                                <span class="fw-semibold text-success">XCG
                                                    {{ number_format($reservation->total_cost, 2) }}</span>
                                            </td>
                                            <td>
                                                <span
                                                    class="badge bg-{{ $reservation->status_color }}-transparent text-{{ $reservation->status_color }}">
                                                    {{ $reservation->status }}
                                                </span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="booking-pagination">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="pagination-info">
                                    Showing {{ $reservations->firstItem() }} to {{ $reservations->lastItem() }}
                                    of {{ $reservations->total() }} reservations
                                </div>
                                <div class="admin-pagination">
                                    {{ $reservations->links() }}
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="mb-3">
                                <i class="ti ti-calendar-x fs-48 text-muted"></i>
                            </div>
                            <h5 class="text-muted">No Reservations Found</h5>
                            <p class="text-muted">There are no upcoming reservations for this week.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>



    <!-----Next Week Reservations Table------>
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="ti ti-list me-2"></i>Next week's Reservations
                    </div>
                </div>
                @php
                    // use App\Models\Reservation;
                    // use Carbon\Carbon;
                    $nextQuery = Reservation::with('field', 'user');
                    $nextWeekReservations = $nextQuery
                        ->whereIn('status', ['Pending', 'Confirmed', 'Cancelled'])
                        ->whereBetween('booking_date', [Carbon::now()->addWeek()->startOfWeek(), Carbon::now()->addWeek()->endOfWeek()])
                        ->orderBy('booking_date', 'asc')
                        ->orderBy('start_time', 'asc')
                        ->paginate(10);
                @endphp

                <div class="card-body">
                    @if ($nextWeekReservations->count() > 0)

                        <div class="table-responsive">
                            <table class="table text-nowrap table-hover">
                                <thead>
                                    <tr>
                                        <th>Field</th>
                                        <th>Date & Time</th>
                                        <th>Customer</th>
                                        <th>Cost</th>
                                        <th>Status</th>
                                        <!--<th>Actions</th>-->
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($nextWeekReservations as $reservation)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-2">
                                                        <span class="avatar avatar-sm bg-light">
                                                            <i class="ti ti-building-stadium fs-12"></i>
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <span class="fw-semibold">{{ $reservation->field->name }}</span>
                                                        <br>
                                                        <small class="text-muted">{{ $reservation->field->type }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <span class="fw-semibold">{{ $reservation->booking_date->format('M d, Y') }}</span>
                                                    <br>
                                                    <small class="text-muted">{{ $reservation->time_range }}</small>
                                                    <br>
                                                    <span class="badge bg-primary-transparent text-primary">Duration: {{ $reservation->duration_hours }} {{ Str::plural('hour', $reservation->duration_hours) }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-semibold">{{ $reservation->customer_display_name }}</span>
                                            </td>
                                            <td>
                                                <span class="fw-semibold text-success">XCG {{ number_format($reservation->total_cost, 2) }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $reservation->status_color }}-transparent text-{{ $reservation->status_color }}">
                                                    {{ $reservation->status }}
                                                </span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="booking-pagination">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="pagination-info">
                                    Showing {{ $nextWeekReservations->firstItem() }} to {{ $nextWeekReservations->lastItem() }} of {{ $nextWeekReservations->total() }} reservations
                                </div>
                                <div class="admin-pagination">
                                    {{ $nextWeekReservations->links() }}
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="mb-3">
                                <i class="ti ti-calendar-x fs-48 text-muted"></i>
                            </div>
                            <h5 class="text-muted">No Reservations Found</h5>
                            <p class="text-muted">There are no reservations for next week.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection
