<?php

namespace App\Http\Controllers;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\Utility;
use App\Rules\ActiveUtilityRule;
use App\Services\FieldAvailabilityService;
use App\Services\ReservationCostService;
use App\Services\ReservationValidationService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ReservationController extends Controller
{
    protected FieldAvailabilityService $availabilityService;

    protected ReservationCostService $costService;

    protected ReservationValidationService $validationService;

    public function __construct(
        FieldAvailabilityService $availabilityService,
        ReservationCostService $costService,
        ReservationValidationService $validationService
    ) {
        $this->availabilityService = $availabilityService;
        $this->costService = $costService;
        $this->validationService = $validationService;
    }

    /**
     * Display a listing of the user's reservations.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $query = Reservation::with('field', 'user');

        // ***********mark as complete when current date is higher than confirmed reservation date*******************//
        $today = Carbon::today();

        // Update all confirmed reservations in the past to 'Completed'
        Reservation::where('status', 'Confirmed')
            ->whereDate('booking_date', '<', $today)
            ->update(['status' => 'Completed']);
        // ***********mark as complete when current date is higher than confirmed reservation date*******************//

        // Get user's reservations with field information
        /*$reservations = Reservation::with('field')
            ->forUser($user->id)
            ->orderBy('booking_date', 'desc')
            ->orderBy('start_time', 'desc')
            ->paginate(10);*/
        // ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range (ensure end date is inclusive)
        if ($request->filled('date_from')) {
            $query->whereDate('booking_date', '>=', Carbon::parse($request->date_from)->toDateString());
        }
        if ($request->filled('date_to')) {
            // Use '<' on the next day to guarantee inclusive behavior regardless of DB driver/timezone
            $inclusiveEnd = Carbon::parse($request->date_to)->addDay()->toDateString();
            $query->whereDate('booking_date', '<', $inclusiveEnd);
        }

        // Filter by field
        if ($request->filled('field_id')) {
            $query->where('field_id', $request->field_id);
        }
        // /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

        // All users (admin or not) can see all reservations
        $reservations = $query
            ->orderBy('booking_date', 'desc')
            ->orderBy('start_time', 'desc')
            ->paginate(10);

        // Get upcoming reservations count for dashboard
        $upcomingCount = Reservation::upcoming()
            ->active()
            ->count();

        // Get total reservations count (unfiltered) for the "Total Reservations" card
        $totalReservationsCount = Reservation::count();

        $fields = Field::active()->orderBy('name')->get();

        return view('reservations.index', compact('reservations', 'upcomingCount', 'totalReservationsCount', 'fields'));
    }

    /**
     * Show the form for creating a new reservation.
     */
    public function create(Request $request)
    {
        // ///////////// pa wak si e field ta under maintenance
        $fields1 = Field::all();

        foreach ($fields1 as $field1) {
            $field1->updateStatusBasedOnDates();
        }
        // ////////////////////////////////////////////////////////////////////
        // Get available fields
        $fields = Field::where('status', 'Active')->orderBy('name')->get();

        // Get active utilities
        $utilities = Utility::where('is_active', true)->orderBy('name')->get();

        // Get selected field if provided
        $selectedField = null;
        if ($request->has('field_id')) {
            $selectedField = Field::find($request->field_id);
        }

        // Get selected date if provided (handle both 'date' and 'booking_date' parameters)
        $selectedDate = $request->get('date') ?: $request->get('booking_date', now()->addDay()->format('Y-m-d'));

        // Get selected time if provided
        $selectedTime = $request->get('time') ?: $request->get('start_time');

        // Convert full time format to HH:MM if needed (FullCalendar sends "14:00:00")
        if ($selectedTime && strlen($selectedTime) > 5) {
            $selectedTime = substr($selectedTime, 0, 5); // "14:00:00" -> "14:00"
        }

        // Get selected duration if provided
        $selectedDuration = $request->get('duration_hours', 1);

        // Get field availability for the next 7 days if a field is selected
        $fieldAvailability = [];
        if ($selectedField) {
            $startDate = Carbon::parse($selectedDate);
            $endDate = $startDate->copy()->addDays(6);
            $fieldAvailability = $this->availabilityService->getFieldAvailabilityCalendar($selectedField, $startDate, $endDate);
        }

        return view('reservations.create', compact('fields', 'utilities', 'selectedField', 'selectedDate', 'selectedTime', 'selectedDuration', 'fieldAvailability'));
    }

    /**
     * Store a newly created reservation in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'field_id' => ['required', 'exists:fields,id'],
            'booking_date' => ['required', 'date', 'after_or_equal:today'],
            'start_time' => ['required', 'regex:/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9](?::[0-5][0-9])?$/'],
            'start_time1' => ['required', 'regex:/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9](?::[0-5][0-9])?$/'],
            'duration_hours' => ['required', 'numeric', 'min:0.5', 'regex:/^[0-9]+(\.[05])?$/'], // Allow half-hour increments
            'customer_name' => ['nullable', 'string', 'max:255'],
            'customer_email' => ['nullable', 'email', 'max:255'],
            'customer_phone' => ['nullable', 'string', 'max:20'],
            'special_requests' => ['nullable', 'string', 'max:1000'],
            'utilities' => ['nullable', 'array'],
            'utilities.*.id' => ['required_with:utilities', new ActiveUtilityRule],
            'utilities.*.hours' => ['required_with:utilities', 'integer', 'min:1'], // Utilities must be whole number quantities
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            return back()->withErrors($validator)->withInput();
        }

        $field = Field::findOrFail($request->field_id);

        // Calculate end time (supports half-hour increments)
        $startTime = Carbon::parse($request->start_time);
        $durationMinutes = (float) $request->duration_hours * 60;
        $endTime = $startTime->copy()->addMinutes($durationMinutes);

        // Validate working hours
        if (! $field->isWithinWorkingHours($request->start_time, $endTime->format('H:i'))) {
            $error = [
                'start_time' => 'Reservation must be within field working hours ('.
                               $field->opening_time.' - '.$field->closing_time.')',
            ];
            if ($request->expectsJson()) {
                return response()->json(['errors' => $error], 422);
            }

            return back()->withErrors($error)->withInput();
        }

        // Validate duration
        if (! $field->isValidDuration($request->duration_hours)) {
            $error = [
                'duration_hours' => 'Duration must be between '.$field->min_booking_hours.
                                   ' and '.$field->max_booking_hours.' hours for this field.',
            ];
            if ($request->expectsJson()) {
                return response()->json(['errors' => $error], 422);
            }

            return back()->withErrors($error)->withInput();
        }

        // ///////////////////////////////////////////////////////////////////////////////////////////////////////////
        // check pa e no book 5 minute prome ku e orario
        // Combine booking_date and start_time into a full datetime
        $reservationDateTime = Carbon::parse($request->booking_date)
            ->setTimeFromTimeString($request->start_time);

        // Prevent booking if reservation starts in less than 5 minutes
        if (now()->diffInMinutes($reservationDateTime, false) < 5) {
            $error = [
                'start_time' => 'You cannot book a reservation less than 5 minutes before it starts.',
            ];
            if ($request->expectsJson()) {
                return response()->json(['errors' => $error], 422);
            }

            return back()->withErrors($error)->withInput();
        }
        // ///////////////////////////////////////////////////////////////////////////////////////////////////////////

        // Check availability
        if (! $field->isAvailableAt($request->booking_date, $request->start_time, $endTime->format('H:i'))) {
            $error = [
                'start_time' => 'The selected time slot is not available. Please choose a different time.',
            ];
            if ($request->expectsJson()) {
                return response()->json(['errors' => $error], 422);
            }

            return back()->withErrors($error)->withInput();
        }

        // Calculate total cost using server-side authoritative calculation
        $utilities = $request->utilities ?? [];
        $costCalculation = $this->costService->calculateTotalCostWithUtilities(
            $field,
            $request->duration_hours,
            $request->start_time,
            $utilities
        );

        $totalCost = $costCalculation['total_cost'];
        $utilityData = $costCalculation['utility_breakdown'];

        // Create reservation
        $reservation = Reservation::create([
            'field_id' => $request->field_id,
            'user_id' => auth()->id(),
            'booking_date' => $request->booking_date,
            'start_time' => $request->start_time,
            'end_time' => $endTime->format('H:i'),
            'duration_hours' => $request->duration_hours,
            'total_cost' => $totalCost,
            'status' => 'Pending',
            'customer_name' => $request->customer_name ?: auth()->user()->name,
            'customer_email' => $request->customer_email ?: auth()->user()->email,
            'customer_phone' => $request->customer_phone,
            'special_requests' => $request->special_requests,
        ]);

        // /////////////////////////////////////////////////////////////////////////////////////////////////////
        // Save utilities (assuming pivot table `reservation_utility`)
        foreach ($utilityData as $data) {
            $reservation->utilities()->attach($data['utility_id'], [
                'hours' => $data['hours'],
                'rate' => $data['rate'],
                'cost' => $data['cost'],
            ]);
        }
        // /////////////////////////////////////////////////////////////////////////////////////////////////////

        // Auto-confirm for Phase 1 (no approval workflow)
        $reservation->autoConfirm();

        return redirect()->route('reservations.show', $reservation)
            ->with('success', 'Reservation created successfully! Your reservation has been confirmed.');
    }

    /**
     * Display the specified reservation.
     */
    public function show(Reservation $reservation)
    {
        $user = auth()->user();

        // Allow admins OR the user who owns the reservation
        if (! $user->isAdmin() && $reservation->user_id !== auth()->id()) {
            abort(403, 'You can only view your own reservations.');
        }

        $reservation->load('field', 'utilities');

        // If this is an AJAX request, return JSON data
        if (request()->ajax() || request()->wantsJson()) {
            return $this->getReservationDetailsJson($reservation);
        }

        return view('reservations.show', compact('reservation'));
    }

    /**
     * Get reservation details as JSON for AJAX requests
     */
    private function getReservationDetailsJson(Reservation $reservation)
    {
        // Calculate cost breakdown
        $costService = new ReservationCostService;
        $costBreakdown = $costService->getCostBreakdown(
            $reservation->field,
            $reservation->duration_hours,
            $reservation->start_time
        );
        $utilityTotal = $reservation->utilities->sum('pivot.cost');

        return response()->json([
            'id' => $reservation->id,
            'status' => $reservation->status,
            'status_color' => $reservation->status_color,
            'can_be_modified' => $reservation->canBeModified(),
            'can_be_cancelled' => $reservation->canBeCancelled(),

            // Field information
            'field' => [
                'id' => $reservation->field->id,
                'name' => $reservation->field->name,
                'type' => $reservation->field->type,
                'capacity' => $reservation->field->capacity,
                'hourly_rate' => $reservation->field->hourly_rate,
                'night_hourly_rate' => $reservation->field->night_hourly_rate,
                'description' => $reservation->field->description ?: 'No description available',
            ],

            // Schedule information
            'booking_date' => $reservation->booking_date->format('l, F d, Y'),
            'time_range' => $reservation->time_range,
            'duration_hours' => $reservation->duration_hours,
            'formatted_date_time' => $reservation->formatted_date_time,

            // Customer information
            'customer_display_name' => $reservation->customer_display_name,
            'customer_email' => $reservation->customer_email ?: 'Not provided',
            'customer_phone' => $reservation->customer_phone ?: 'Not provided',
            'special_requests' => $reservation->special_requests,

            // Cost information
            'total_cost' => $reservation->total_cost,
            'cost_breakdown' => $costBreakdown,
            'utility_total' => $utilityTotal,

            // Utilities
            'utilities' => $reservation->utilities->map(function ($utility) {
                return [
                    'name' => $utility->name,
                    'hours' => $utility->pivot->hours,
                    'rate' => $utility->pivot->rate,
                    'cost' => $utility->pivot->cost,
                ];
            }),

            // Timeline information
            'created_at' => $reservation->created_at->format('M d, Y H:i'),
            'confirmed_at' => $reservation->confirmed_at?->format('M d, Y H:i'),
            'cancelled_at' => $reservation->cancelled_at?->format('M d, Y H:i'),

            // URLs for actions
            'edit_url' => route('reservations.edit', $reservation),
            'cancel_url' => route('reservations.cancel', $reservation),
            'book_same_field_url' => route('reservations.create', ['field_id' => $reservation->field_id]),
        ]);
    }

    /**
     * Show the form for editing the specified reservation.
     */
    public function edit(Reservation $reservation)
    {
        // Authorization check
        if ($reservation->user_id !== auth()->id()) {
            abort(403, 'You can only edit your own reservations.');
        }

        // Check if reservation can be modified
        if (! $reservation->canBeModified()) {
            return back()->with('error', 'This reservation cannot be modified. Reservations can only be changed up to 24 hours before the scheduled time.');
        }
        // ////////////////////////////////////////////////////////
        // $fields = Field::all();
        $utilities = Utility::all();

        $reservationUtilities = $reservation->utilities->map(function ($utility) {
            return [
                'id' => $utility->id,
                'name' => $utility->name,
                'rate' => $utility->pivot->rate,
                'hours' => $utility->pivot->hours,
                'cost' => $utility->pivot->cost,
            ];
        });
        // ////////////////////////////////////////////////////////
        $fields = Field::where('status', 'Active')->orderBy('name')->get();

        return view('reservations.edit', compact('reservation', 'fields', 'utilities', 'reservationUtilities'));
    }

    /**
     * Update the specified reservation in storage.
     */
    public function update(Request $request, Reservation $reservation)
    {
        // Authorization check
        if ($reservation->user_id !== auth()->id()) {
            abort(403, 'You can only edit your own reservations.');
        }

        // Check if reservation can be modified
        if (! $reservation->canBeModified()) {
            return back()->with('error', 'This reservation cannot be modified. Reservations can only be changed up to 24 hours before the scheduled time.');
        }

        $validator = Validator::make($request->all(), [
            'field_id' => ['required', 'exists:fields,id'],
            'booking_date' => ['required', 'date', 'after_or_equal:today'],
            'start_time' => ['required', 'regex:/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9](?::[0-5][0-9])?$/'],
            'duration_hours' => ['required', 'numeric', 'min:0.5', 'regex:/^[0-9]+(\.[05])?$/'], // Allow half-hour increments
            'customer_name' => ['nullable', 'string', 'max:255'],
            'customer_email' => ['nullable', 'email', 'max:255'],
            'customer_phone' => ['nullable', 'string', 'max:20'],
            'special_requests' => ['nullable', 'string', 'max:1000'],
            'utilities' => ['nullable', 'array'],
            'utilities.*.id' => ['required_with:utilities', new ActiveUtilityRule],
            'utilities.*.hours' => ['required_with:utilities', 'integer', 'min:1'], // Utilities must be whole number quantities
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $field = Field::findOrFail($request->field_id);

        // Calculate end time (supports half-hour increments)
        $startTime = Carbon::parse($request->start_time);
        $durationMinutes = (float) $request->duration_hours * 60;
        $endTime = $startTime->copy()->addMinutes($durationMinutes);

        // Validate working hours
        if (! $field->isWithinWorkingHours($request->start_time, $endTime->format('H:i'))) {
            return back()->withErrors([
                'start_time' => 'Reservation must be within field working hours ('.
                               $field->opening_time.' - '.$field->closing_time.')',
            ])->withInput();
        }

        // Validate duration
        if (! $field->isValidDuration($request->duration_hours)) {
            return back()->withErrors([
                'duration_hours' => 'Duration must be between '.$field->min_booking_hours.
                                   ' and '.$field->max_booking_hours.' hours for this field.',
            ])->withInput();
        }

        // Check availability (excluding current reservation)
        if (! $field->isAvailableAt($request->booking_date, $request->start_time, $endTime->format('H:i'), $reservation->id)) {
            return back()->withErrors([
                'start_time' => 'The selected time slot is not available. Please choose a different time.',
            ])->withInput();
        }

        // Calculate total cost using server-side authoritative calculation (consistent with store method)
        $utilities = $request->utilities ?? [];
        $costCalculation = $this->costService->calculateTotalCostWithUtilities(
            $field,
            $request->duration_hours,
            $request->start_time,
            $utilities
        );

        $totalCost = $costCalculation['total_cost'];
        $utilityData = $costCalculation['utility_breakdown'];

        // Update utilities using server-calculated data
        $reservation->utilities()->detach(); // Clear old utilities

        foreach ($utilityData as $utility) {
            $reservation->utilities()->attach($utility['utility_id'], [
                'hours' => $utility['hours'],
                'rate' => $utility['rate'],
                'cost' => $utility['cost'],
            ]);
        }

        // Update reservation
        $reservation->update([
            'field_id' => $request->field_id,
            'booking_date' => $request->booking_date,
            'start_time' => $request->start_time,
            'end_time' => $endTime->format('H:i'),
            'duration_hours' => $request->duration_hours,
            'total_cost' => $totalCost,
            'customer_name' => $request->customer_name ?: auth()->user()->name,
            'customer_email' => $request->customer_email ?: auth()->user()->email,
            'customer_phone' => $request->customer_phone,
            'special_requests' => $request->special_requests,
        ]);

        return redirect()->route('reservations.show', $reservation)
            ->with('success', 'Reservation updated successfully!');
    }

    /**
     * Cancel the specified reservation.
     */
    public function cancel(Reservation $reservation)
    {
        // Authorization check
        if ($reservation->user_id !== auth()->id()) {
            abort(403, 'You can only cancel your own reservations.');
        }

        if (! $reservation->canBeCancelled()) {
            return back()->with('error', 'This reservation cannot be cancelled. Reservations can only be cancelled up to 24 hours before the scheduled time.');
        }

        $reservation->cancel();

        return back()->with('success', 'Reservation cancelled successfully.');
    }

    /**
     * UN-Cancel the specified reservation.
     */
    public function uncancel(Reservation $reservation)
    {
        // Authorization check: only the owner can uncancel (adjust if admins can too)
        if ($reservation->user_id !== auth()->id()) {
            abort(403, 'You can only restore your own reservations.');
        }

        if (! $reservation->isCancelled()) {
            return back()->with('error', 'This reservation is not cancelled.');
        }

        $reservation->uncancel();

        return back()->with('success', 'Reservation restored successfully.');
    }

    /*******************************************************************************************************
     * Get field availability for AJAX requests
     */
    public function checkAvailability(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'field_id' => ['required', 'exists:fields,id'],
            'date' => ['required', 'date'],
            'duration_hours' => ['required', 'numeric', 'min:0.5', 'max:8', 'regex:/^[0-9]+(\.[05])?$/'], // Allow half-hour increments
            'exclude_reservation_id' => ['nullable', 'integer'],
        ]);

        if ($validator->fails()) {
            return response()->json(['available' => false, 'message' => 'Invalid input', 'slots' => []]);
        }

        $field = Field::findOrFail($request->field_id);
        $durationHours = (float) $request->duration_hours;
        $excludeReservationId = $request->exclude_reservation_id;

        // Get available time slots for the duration (excluding specified reservation if provided)
        $slots = $this->availabilityService->getAvailableTimeSlots($field, $request->date, $durationHours, $excludeReservationId);

        return response()->json([
            'available' => count($slots) > 0,
            'message' => count($slots) > 0 ? count($slots).' time slots available' : 'No available time slots',
            'slots' => $slots,
        ]);
    }

    /**
     * Get cost estimate for AJAX requests
     */
    public function getCostEstimate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'field_id' => ['required', 'exists:fields,id'],
            'duration_hours' => ['required', 'numeric', 'min:0.5', 'max:8', 'regex:/^[0-9]+(\.[05])?$/'], // Allow half-hour increments
            'start_time' => ['nullable', 'regex:/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9](?::[0-5][0-9])?$/'],
            'start_time1' => ['nullable', 'regex:/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9](?::[0-5][0-9])?$/'],
            'utilities' => ['nullable', 'array'],
            'utilities.*.id' => ['required_with:utilities', 'exists:utilities,id'],
            'utilities.*.hours' => ['required_with:utilities', 'integer', 'min:1'], // Utilities must be whole number quantities
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => 'Invalid input']);
        }

        $estimate = $this->costService->getReservationEstimate(
            (int) $request->field_id,
            (float) $request->duration_hours,
            $request->start_time,
            $request->utilities ?? []
        );

        return response()->json($estimate);
    }
}
